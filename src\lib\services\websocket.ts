import { browser } from '$app/environment';
import { auth } from '$lib/stores/auth';
import { get } from 'svelte/store';

export interface SSEMessage {
	type: string;
	[key: string]: any;
}

export interface ChatMessage {
	_id: string;
	message: string;
	sender: string;
	receiver?: string;
	group?: string;
	timestamp: string;
	attachments?: any[];
}

class RealtimeService {
	private eventSource: EventSource | null = null;
	private reconnectAttempts = 0;
	private maxReconnectAttempts = 5;
	private reconnectDelay = 1000; // Start with 1 second
	private isConnecting = false;
	private messageHandlers = new Map<string, ((data: any) => void)[]>();
	private currentChatId: string | null = null;
	private currentChatType: 'user' | 'group' | null = null;
	private authUnsubscribe: (() => void) | null = null;

	constructor() {
		// Don't auto-connect in constructor to prevent duplicate connections
		// Connection will be managed explicitly by the chat page
	}

	connect(token?: string) {
		if (!browser) {
			console.log('WebSocket: Not in browser environment');
			return;
		}

		// If already connected and working, don't reconnect
		if (this.eventSource && this.eventSource.readyState === EventSource.OPEN) {
			console.log('WebSocket: Already connected');
			return;
		}

		// If currently connecting, don't start another connection
		if (this.isConnecting) {
			console.log('WebSocket: Already connecting');
			return;
		}

		// Get token from auth store if not provided
		if (!token) {
			const authState = get(auth);
			token = authState.token;
			if (!token) {
				console.error('WebSocket: No token available for connection');
				return;
			}
		}

		console.log('WebSocket: Using token (first 20 chars):', token.substring(0, 20) + '...');

		this.isConnecting = true;
		console.log('WebSocket: Starting connection...');

		try {
			// Close any existing connection first
			if (this.eventSource) {
				this.eventSource.close();
				this.eventSource = null;
			}

			// Create SSE connection with token as query parameter
			const sseUrl = `/api/ws?token=${encodeURIComponent(token)}`;

			console.log('WebSocket: Connecting to SSE (URL length):', sseUrl.length);
			this.eventSource = new EventSource(sseUrl);

			this.eventSource.onopen = () => {
				console.log('WebSocket: SSE connected successfully');
				this.isConnecting = false;
				this.reconnectAttempts = 0;
				this.reconnectDelay = 1000;
			};

			this.eventSource.onmessage = (event) => {
				try {
					const message: SSEMessage = JSON.parse(event.data);
					this.handleMessage(message);
				} catch (error) {
					console.error('WebSocket: Error parsing SSE message:', error);
				}
			};

			this.eventSource.onerror = (error) => {
				console.error('WebSocket: SSE error:', error);
				this.isConnecting = false;

				// Only attempt reconnection if the connection is actually closed
				// EventSource automatically reconnects for network errors, but not for auth errors
				if (this.eventSource?.readyState === EventSource.CLOSED) {
					this.eventSource = null;
					if (this.reconnectAttempts < this.maxReconnectAttempts) {
						console.log(`WebSocket: Connection closed, scheduling reconnect attempt ${this.reconnectAttempts + 1}`);
						this.scheduleReconnect(token);
					} else {
						console.error('WebSocket: Max reconnection attempts reached, giving up');
					}
				} else {
					// For other errors, just log them - EventSource will handle reconnection
					console.log('WebSocket: Non-fatal error, EventSource will handle reconnection');
				}
			};

		} catch (error) {
			console.error('Error creating SSE connection:', error);
			this.isConnecting = false;
		}
	}

	private scheduleReconnect(token: string) {
		this.reconnectAttempts++;

		// Limit reconnection attempts to prevent infinite loops
		if (this.reconnectAttempts > this.maxReconnectAttempts) {
			console.log('WebSocket: Max reconnection attempts reached, stopping');
			return;
		}

		const delay = Math.min(this.reconnectDelay * this.reconnectAttempts, 10000); // Cap at 10 seconds

		console.log(`WebSocket: Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

		setTimeout(() => {
			if (this.reconnectAttempts <= this.maxReconnectAttempts) {
				this.connect(token);
			}
		}, delay);
	}

	disconnect() {
		console.log('WebSocket: Disconnecting service');

		// Clear all reconnection attempts
		this.reconnectAttempts = this.maxReconnectAttempts + 1;

		if (this.eventSource) {
			// Remove all event listeners to prevent memory leaks
			this.eventSource.onopen = null;
			this.eventSource.onmessage = null;
			this.eventSource.onerror = null;

			this.eventSource.close();
			this.eventSource = null;
			console.log('WebSocket: EventSource closed and cleared');
		}

		this.isConnecting = false;
		this.reconnectAttempts = 0;
		this.currentChatId = null;
		this.currentChatType = null;

		// Clear auth subscription if exists
		if (this.authUnsubscribe) {
			this.authUnsubscribe();
			this.authUnsubscribe = null;
		}

		// Clear all message handlers to prevent memory leaks
		this.messageHandlers.clear();
		console.log('WebSocket: All handlers and subscriptions cleared');
	}

	private handleMessage(message: SSEMessage) {
		console.log('Received SSE message:', message);

		// Call registered handlers for this message type
		const handlers = this.messageHandlers.get(message.type) || [];
		handlers.forEach(handler => {
			try {
				handler(message);
			} catch (error) {
				console.error('Error in message handler:', error);
			}
		});
	}

	// Register a handler for a specific message type
	on(messageType: string, handler: (data: any) => void) {
		if (!this.messageHandlers.has(messageType)) {
			this.messageHandlers.set(messageType, []);
		}
		this.messageHandlers.get(messageType)!.push(handler);

		// Return unsubscribe function
		return () => {
			const handlers = this.messageHandlers.get(messageType);
			if (handlers) {
				const index = handlers.indexOf(handler);
				if (index > -1) {
					handlers.splice(index, 1);
				}
			}
		};
	}

	// Join a specific chat (user or group)
	joinChat(chatId: string, chatType: 'user' | 'group') {
		this.currentChatId = chatId;
		this.currentChatType = chatType;
		// SSE doesn't need to send join messages - server broadcasts to all relevant users
	}

	// Leave current chat
	leaveChat() {
		this.currentChatId = null;
		this.currentChatType = null;
		// SSE doesn't need to send leave messages
	}

	// Force reconnection (useful for refresh scenarios)
	forceReconnect() {
		console.log('WebSocket: Force reconnecting...');
		this.disconnect();
		this.reconnectAttempts = 0; // Reset attempts

		// Get token and reconnect
		const authState = get(auth);
		if (authState.token) {
			setTimeout(() => this.connect(authState.token), 100);
		}
	}

	// Get connection status
	get isConnected() {
		return this.eventSource && this.eventSource.readyState === EventSource.OPEN;
	}

	get connectionState() {
		if (!this.eventSource) return 'disconnected';
		if (this.isConnecting) return 'connecting';

		switch (this.eventSource.readyState) {
			case EventSource.CONNECTING: return 'connecting';
			case EventSource.OPEN: return 'connected';
			case EventSource.CLOSED: return 'disconnected';
			default: return 'unknown';
		}
	}
}

// Create singleton instance
export const websocketService = new RealtimeService();

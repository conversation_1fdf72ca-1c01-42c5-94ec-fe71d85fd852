// Store active SSE connections
const connections = new Map<string, { 
	controller: ReadableStreamDefaultController; 
	username: string; 
	userId: string;
	chatId?: string;
	chatType?: 'user' | 'group';
}>();

// Add a connection
export function addConnection(connectionId: string, connection: {
	controller: ReadableStreamDefaultController;
	username: string;
	userId: string;
	chatId?: string;
	chatType?: 'user' | 'group';
}) {
	connections.set(connectionId, connection);
}

// Remove a connection
export function removeConnection(connectionId: string) {
	connections.delete(connectionId);
}

// Broadcast message to specific users
export function broadcastToUsers(usernames: string[], message: any) {
	const messageStr = `data: ${JSON.stringify(message)}\n\n`;
	
	for (const [connectionId, connection] of connections) {
		if (usernames.includes(connection.username)) {
			try {
				connection.controller.enqueue(new TextEncoder().encode(messageStr));
			} catch (error) {
				console.error('Error sending message to user:', connection.username, error);
				// Remove dead connection
				connections.delete(connectionId);
			}
		}
	}
}

// Broadcast message to all connected users
export function broadcastToAll(message: any) {
	const messageStr = `data: ${JSON.stringify(message)}\n\n`;
	
	for (const [connectionId, connection] of connections) {
		try {
			connection.controller.enqueue(new TextEncoder().encode(messageStr));
		} catch (error) {
			console.error('Error broadcasting message:', error);
			connections.delete(connectionId);
		}
	}
}

// Get connection count
export function getConnectionCount() {
	return connections.size;
}

// Get connected users
export function getConnectedUsers() {
	return Array.from(connections.values()).map(conn => conn.username);
}

// Store active SSE connections
const connections = new Map<string, {
	controller: ReadableStreamDefaultController;
	username: string;
	userId: string;
	chatId?: string;
	chatType?: 'user' | 'group';
}>();

// Add a connection
export function addConnection(connectionId: string, connection: {
	controller: ReadableStreamDefaultController;
	username: string;
	userId: string;
	chatId?: string;
	chatType?: 'user' | 'group';
}) {
	connections.set(connectionId, connection);
}

// Remove a connection
export function removeConnection(connectionId: string) {
	connections.delete(connectionId);
}

// Helper function to check if controller is still valid
function isControllerValid(controller: ReadableStreamDefaultController): boolean {
	try {
		// Check if the controller's stream is still readable
		return controller.desiredSize !== null;
	} catch {
		return false;
	}
}

// Broadcast message to specific users
export function broadcastToUsers(usernames: string[], message: any) {
	const messageStr = `data: ${JSON.stringify(message)}\n\n`;
	const deadConnections: string[] = [];

	for (const [connectionId, connection] of connections) {
		if (usernames.includes(connection.username)) {
			// Check if controller is still valid before trying to send
			if (!isControllerValid(connection.controller)) {
				console.log('Removing dead connection for user:', connection.username);
				deadConnections.push(connectionId);
				continue;
			}

			try {
				connection.controller.enqueue(new TextEncoder().encode(messageStr));
			} catch (error) {
				console.error('Error sending message to user:', connection.username, error);
				deadConnections.push(connectionId);
			}
		}
	}

	// Clean up dead connections
	deadConnections.forEach(id => connections.delete(id));
}

// Broadcast message to all connected users
export function broadcastToAll(message: any) {
	const messageStr = `data: ${JSON.stringify(message)}\n\n`;
	const deadConnections: string[] = [];

	for (const [connectionId, connection] of connections) {
		// Check if controller is still valid before trying to send
		if (!isControllerValid(connection.controller)) {
			console.log('Removing dead connection for user:', connection.username);
			deadConnections.push(connectionId);
			continue;
		}

		try {
			connection.controller.enqueue(new TextEncoder().encode(messageStr));
		} catch (error) {
			console.error('Error broadcasting message:', error);
			deadConnections.push(connectionId);
		}
	}

	// Clean up dead connections
	deadConnections.forEach(id => connections.delete(id));
}

// Get connection count
export function getConnectionCount() {
	return connections.size;
}

// Get connected users
export function getConnectedUsers() {
	return Array.from(connections.values()).map(conn => conn.username);
}

// Cleanup dead connections periodically
export function cleanupDeadConnections() {
	const deadConnections: string[] = [];

	for (const [connectionId, connection] of connections) {
		if (!isControllerValid(connection.controller)) {
			deadConnections.push(connectionId);
		}
	}

	deadConnections.forEach(id => {
		const connection = connections.get(id);
		if (connection) {
			console.log('Cleaning up dead connection for user:', connection.username);
			connections.delete(id);
		}
	});

	return deadConnections.length;
}

// Start periodic cleanup (call this once when the server starts)
let cleanupInterval: NodeJS.Timeout | null = null;

export function startPeriodicCleanup() {
	if (cleanupInterval) return; // Already started

	cleanupInterval = setInterval(() => {
		const cleaned = cleanupDeadConnections();
		if (cleaned > 0) {
			console.log(`Cleaned up ${cleaned} dead connections`);
		}
	}, 30000); // Clean up every 30 seconds
}

export function stopPeriodicCleanup() {
	if (cleanupInterval) {
		clearInterval(cleanupInterval);
		cleanupInterval = null;
	}
}

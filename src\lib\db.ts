import mongoose from 'mongoose';

let isConnected = false;
let isConnecting = false;

export async function connect() {
	const mongoURI = process.env.MONGODB_URI || 'mongodb://127.0.0.1:27017/konekt';

	// If already connected, return early
	if (isConnected && mongoose.connection.readyState === 1) {
		return;
	}

	// If currently connecting, just return - don't wait to avoid infinite loops
	if (isConnecting) {
		console.log('MongoDB: Already connecting, skipping...');
		return;
	}

	isConnecting = true;

	try {
		console.log('MongoDB: Connecting to database...');
		// Add connection options for better reliability
		await mongoose.connect(mongoURI, {
			serverSelectionTimeoutMS: 5000, // 5 second timeout
			socketTimeoutMS: 45000, // 45 second socket timeout
			maxPoolSize: 10, // Maintain up to 10 socket connections
			minPoolSize: 5, // Maintain a minimum of 5 socket connections
			maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
		});
		isConnected = true;
		isConnecting = false;
		console.log('MongoDB connected successfully');

		// Handle connection events (only set once)
		if (!mongoose.connection.listeners('error').length) {
			mongoose.connection.on('error', (error) => {
				console.error('MongoDB connection error:', error);
				isConnected = false;
				isConnecting = false;
			});

			mongoose.connection.on('disconnected', () => {
				console.log('MongoDB disconnected');
				isConnected = false;
				isConnecting = false;
			});
		}

	} catch (error) {
		console.error('MongoDB connection error:', error);
		isConnected = false;
		isConnecting = false;
		throw error;
	}
}

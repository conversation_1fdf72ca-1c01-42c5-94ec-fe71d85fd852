import type { Re<PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import { ENV } from '$lib/config/env';
import { getTokenFromRequest } from '$lib/utils/auth';

export const GET: RequestHandler = async (event) => {
	const token = getTokenFromRequest(event);
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		// Decode token first (no DB connection needed)
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);

		// Connect to database only when needed
		await connect();
		const currentUser = await User.findById(decoded.userId).lean();

		if (!currentUser) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// Find all users except the current user using the username
		const otherUsers = await User.find(
			{
				username: { $ne: currentUser.username }
			},
			'username'
		).lean();

		return json({ users: otherUsers.map((user) => user.username) });
	} catch (err) {
		console.error('Error fetching users:', err);
		return json({ error: 'Failed to fetch users' }, { status: 500 });
	}
};

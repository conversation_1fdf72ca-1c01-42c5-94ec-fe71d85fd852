import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Chat from '$lib/models/Chat';
import User from '$lib/models/User';
import Group from '$lib/models/Group';
import jwt from 'jsonwebtoken';
import { encrypt } from '$lib/utils/encryption';
import r2Storage from '$lib/services/r2Storage';
import type { IAttachment } from '$lib/models/Chat';
import { broadcastToUsers } from '$lib/services/realtime';
import { ENV } from '$lib/config/env';
import { getTokenFromRequest } from '$lib/utils/auth';

export const POST: RequestHandler = async (event) => {
	const token = getTokenFromRequest(event);
	if (!token) {
		return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
	}

	try {
		// Verify the JWT token first
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);

		await connect();

		const senderUser = await User.findById(decoded.userId);
		if (!senderUser) {
			return new Response(JSON.stringify({ error: 'Sender not found' }), { status: 404 });
		}

		let receiver: string | undefined;
		let groupId: string | undefined;
		let message: string;
		let attachments: IAttachment[] = [];

		// Check if request contains files (FormData) or just JSON
		const contentType = event.request.headers.get('content-type');

		if (contentType?.includes('multipart/form-data')) {
			// Handle FormData (with potential attachments)
			const formData = await event.request.formData();

			receiver = formData.get('receiver') as string || undefined;
			groupId = formData.get('groupId') as string || undefined;
			message = formData.get('message') as string || '';

			// Process attachments
			const files = formData.getAll('attachments') as File[];
			for (const file of files) {
				if (file && file.size > 0) {
					// Validate file
					const validation = r2Storage.validateFile(file, 'attachment');
					if (!validation.valid) {
						return new Response(JSON.stringify({ error: validation.error }), { status: 400 });
					}

					// Upload to R2
					const key = r2Storage.generateFileKey(decoded.userId, 'attachment', file.name);
					const uploadResult = await r2Storage.uploadFile(file, key, file.type, {
						userId: decoded.userId,
						uploadType: 'attachment'
					});

					attachments.push({
						key: uploadResult.key,
						url: uploadResult.url,
						filename: file.name,
						contentType: uploadResult.contentType,
						size: uploadResult.size
					});
				}
			}
		} else {
			// Handle JSON (text messages only)
			const body = await event.request.json();
			receiver = body.receiver;
			groupId = body.groupId;
			message = body.message;
		}

		if ((!message || !message.trim()) && attachments.length === 0) {
			return new Response(JSON.stringify({ error: 'Message or attachment required' }), { status: 400 });
		}

		if (!receiver && !groupId) {
			return new Response(JSON.stringify({ error: 'Receiver or group required' }), { status: 400 });
		}

		let newMessage;

		// The message will be automatically encrypted by the mongoose schema
		if (groupId) {
			newMessage = new Chat({
				sender: senderUser._id,
				group: groupId,
				message: message || '',
				attachments: attachments.length > 0 ? attachments : undefined
			});
		} else {
			const receiverUser = await User.findOne({ username: receiver });
			if (!receiverUser) {
				return new Response(JSON.stringify({ error: 'Receiver not found' }), { status: 404 });
			}

			if (senderUser.username === receiverUser.username) {
				return new Response(JSON.stringify({ error: 'Cannot send messages to yourself' }), {
					status: 400
				});
			}

			newMessage = new Chat({
				sender: senderUser._id,
				receiver: receiverUser._id,
				message: message || '',
				attachments: attachments.length > 0 ? attachments : undefined
			});
		}

		await newMessage.save();

		// Populate the sender information for the response
		await newMessage.populate('sender', 'username profilePicture');
		if (newMessage.receiver) {
			await newMessage.populate('receiver', 'username');
		}
		if (newMessage.group) {
			await newMessage.populate('group', 'name');
		}

		// Broadcast the new message via WebSocket
		try {
			if (groupId) {
				// For group messages, get all group members
				const group = await Group.findById(groupId).populate('members', 'username');
				if (group) {
					const memberUsernames = group.members.map((member: any) => member.username);
					broadcastToUsers(memberUsernames, {
						type: 'new_message',
						message: newMessage,
						chatType: 'group',
						chatId: groupId
					});
				}
			} else if (receiver) {
				// For direct messages, broadcast to sender and receiver
				const receiverUser = await User.findOne({ username: receiver });
				if (receiverUser) {
					broadcastToUsers([senderUser.username, receiverUser.username], {
						type: 'new_message',
						message: newMessage,
						chatType: 'user',
						chatId: receiver
					});
				}
			}
		} catch (wsError) {
			console.error('Error broadcasting message via WebSocket:', wsError);
			// Don't fail the request if WebSocket broadcast fails
		}

		return new Response(JSON.stringify({
			success: true,
			message: newMessage
		}), { status: 200 });
	} catch (err) {
		console.error('Error sending message:', err);
		if (err instanceof jwt.JsonWebTokenError) {
			return new Response(JSON.stringify({ error: 'Invalid token' }), { status: 401 });
		}
		return new Response(JSON.stringify({ error: 'Failed to send message' }), { status: 500 });
	}
};

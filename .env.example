# Cloudflare R2 Configuration
# Get these values from your Cloudflare dashboard:
# 1. Go to R2 Object Storage in your Cloudflare dashboard
# 2. Create a bucket (e.g., "konekt")
# 3. Go to "Manage R2 API tokens" to create API credentials
# 4. Set up a custom domain for your bucket (optional but recommended)

R2_ACCOUNT_ID=your_cloudflare_account_id_here
R2_ACCESS_KEY_ID=your_r2_access_key_id_here
R2_SECRET_ACCESS_KEY=your_r2_secret_access_key_here
R2_BUCKET_NAME=konekt
R2_PUBLIC_URL=https://your-custom-domain.com

# JWT Secret (generate a random string)
JWT_SECRET=your_jwt_secret_here

# MongoDB
MONGODB_URI=mongodb://127.0.0.1:27017/konekt

import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import r2Storage from '$lib/services/r2Storage';
import { ENV } from '$lib/config/env';

export const POST: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	try {
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const formData = await request.formData();

		const file = formData.get('file') as File;
		const type = formData.get('type') as 'avatar' | 'attachment';

		if (!file) {
			return json({ error: 'No file provided' }, { status: 400 });
		}

		if (!type || !['avatar', 'attachment'].includes(type)) {
			return json({ error: 'Invalid upload type' }, { status: 400 });
		}

		// Validate file
		const validation = r2Storage.validateFile(file, type);
		if (!validation.valid) {
			return json({ error: validation.error }, { status: 400 });
		}

		// Generate unique key
		const key = r2Storage.generateFileKey(decoded.userId, type, file.name);

		// Upload to R2
		const result = await r2Storage.uploadFile(file, key, file.type, {
			userId: decoded.userId,
			uploadType: type,
			originalName: file.name
		});

		return json({
			success: true,
			file: {
				key: result.key,
				url: result.url,
				filename: file.name,
				contentType: result.contentType,
				size: result.size
			}
		});

	} catch (error) {
		console.error('Upload error:', error);
		return json({ error: 'Upload failed' }, { status: 500 });
	}
};

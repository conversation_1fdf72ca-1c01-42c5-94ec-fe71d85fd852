import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '$lib/models/User';
import { connect } from '$lib/db';
import { ENV } from '$lib/config/env';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		await connect(); // Ensure database is connected

		const { username, password } = await request.json();

		if (!username || !password) {
			return json({ error: 'Username and password are required' }, { status: 400 });
		}

		const user = await User.findOne({ username });
		if (!user) {
			return json({ error: 'Invalid credentials' }, { status: 401 });
		}

		const isPasswordValid = await bcrypt.compare(password, user.password);
		if (!isPasswordValid) {
			return json({ error: 'Invalid credentials' }, { status: 401 });
		}

		const token = jwt.sign({ userId: user._id }, ENV.JWT_SECRET, { expiresIn: '24h' });

		cookies.set('token', token, {
			path: '/',
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production',
			sameSite: 'strict',
			maxAge: 60 * 60 * 24
		});

		return json({ token, username: user.username });
	} catch (err) {
		console.error('Error logging in:', err);
		return json({ error: 'Database connection failed' }, { status: 500 });
	}
};

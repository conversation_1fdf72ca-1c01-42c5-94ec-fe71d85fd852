// Simple test script to check authentication
async function testAuth() {
    try {
        // Test registration first
        console.log('Testing registration...');
        const registerResponse = await fetch('http://localhost:5173/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'testuser',
                password: 'test123'
            })
        });

        const registerData = await registerResponse.json();
        console.log('Register response:', registerData);

        // Test login
        console.log('Testing login...');
        const loginResponse = await fetch('http://localhost:5173/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
                username: 'testuser',
                password: 'test123'
            })
        });

        const loginData = await loginResponse.json();
        console.log('Login response:', loginData);

        if (loginResponse.ok) {
            // Test validation
            console.log('Testing validation...');
            const validateResponse = await fetch('http://localhost:5173/api/auth/validate', {
                credentials: 'include'
            });

            const validateData = await validateResponse.json();
            console.log('Validation response:', validateData);
        }

    } catch (error) {
        console.error('Error:', error);
    }
}

testAuth();

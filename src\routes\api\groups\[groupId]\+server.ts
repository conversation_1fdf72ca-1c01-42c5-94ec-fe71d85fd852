import type { Request<PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Group from '$lib/models/Group';
import Chat from '$lib/models/Chat';
import jwt from 'jsonwebtoken';
import { ENV } from '$lib/config/env';
import mongoose from 'mongoose';

export const DELETE: RequestHandler = async ({ params, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return json({ error: 'Unauthorized' }, { status: 401 });
	}

	const { groupId } = params;
	if (!groupId || !mongoose.Types.ObjectId.isValid(groupId)) {
		return json({ error: 'Invalid group ID' }, { status: 400 });
	}

	try {
		await connect();
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const userId = decoded.userId;

		// Find the group and ensure the user deleting it is the creator
		const group = await Group.findOne({ _id: groupId, createdBy: userId }).populate(
			'members',
			'username'
		); // Populate members for broadcast coordination

		if (!group) {
			// Either group doesn't exist or user is not the creator
			return json({ error: 'Group not found or permission denied' }, { status: 404 });
		}

		const memberUsernames = group.members.map((m: any) => m.username);

		// Delete associated chat messages
		await Chat.deleteMany({ group: groupId });

		// Delete the group
		await Group.deleteOne({ _id: groupId });

		// Return success, include deleted groupId and member usernames for client-side WS broadcast
		return json({
			success: true,
			deletedGroupId: groupId,
			memberUsernames: memberUsernames // Needed for client to tell WS server who to notify
		});
	} catch (err) {
		console.error('Error deleting group:', err);
		if (err instanceof jwt.JsonWebTokenError) {
			return json({ error: 'Invalid token' }, { status: 401 });
		}
		return json({ error: 'Failed to delete group' }, { status: 500 });
	}
};

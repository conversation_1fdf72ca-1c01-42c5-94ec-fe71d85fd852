<script lang="ts">
	import { onMount } from 'svelte';
	import { auth } from '$lib/stores/auth';
	import { goto } from '$app/navigation';

	let currentUsername = '';
	let newUsername = '';
	let currentPassword = '';
	let newPassword = '';
	let confirmPassword = '';
	let profilePicture: File | null = null;
	let previewUrl = '';
	let error = '';
	let success = '';

	auth.subscribe((authState) => {
		currentUsername = authState.username || '';

		// If auth is initialized and user is not logged in, redirect to login
		if (authState.isInitialized && !authState.username) {
			goto('/login');
		}
	});

	onMount(async () => {
		// Wait for auth to be initialized
		const unsubscribe = auth.subscribe(async (authState) => {
			if (authState.isInitialized) {
				if (!authState.username) {
					goto('/login');
					unsubscribe();
					return;
				}

				try {
					// Fetch user profile data
					const profileRes = await fetch('/api/user/profile', {
						credentials: 'include'
					});
					if (profileRes.ok) {
						const profile = await profileRes.json();
						previewUrl = profile.profilePicture || '';
					}
				} catch (err) {
					console.error('Error loading profile:', err);
				}
				unsubscribe();
			}
		});
	});

	function handleFileSelect(event: Event) {
		const input = event.target as HTMLInputElement;
		if (input.files && input.files[0]) {
			profilePicture = input.files[0];
			previewUrl = URL.createObjectURL(input.files[0]);
		}
	}

	async function updateProfile() {
		try {
			const formData = new FormData();
			if (profilePicture) {
				formData.append('profilePicture', profilePicture);
			}
			if (newUsername) {
				formData.append('username', newUsername);
			}

			const res = await fetch('/api/user/profile', {
				method: 'PUT',
				credentials: 'include',
				body: formData
			});

			if (res.ok) {
				success = 'Profile updated successfully';
				error = '';
				if (newUsername) {
					// Get the real token from cookies
					const tokenFromCookie = document.cookie
						.split('; ')
						.find(row => row.startsWith('token='))
						?.split('=')[1];
					if (tokenFromCookie) {
						auth.setAuth(tokenFromCookie, newUsername);
					}
				}
			} else {
				const data = await res.json();
				error = data.error || 'Failed to update profile';
				success = '';
			}
		} catch (err) {
			console.error('Error updating profile:', err);
			error = 'Failed to update profile';
			success = '';
		}
	}

	async function updatePassword() {
		if (newPassword !== confirmPassword) {
			error = 'Passwords do not match';
			return;
		}

		try {
			const res = await fetch('/api/user/password', {
				method: 'PUT',
				headers: { 'Content-Type': 'application/json' },
				credentials: 'include',
				body: JSON.stringify({
					currentPassword,
					newPassword
				})
			});

			if (res.ok) {
				success = 'Password updated successfully';
				error = '';
				currentPassword = '';
				newPassword = '';
				confirmPassword = '';
			} else {
				const data = await res.json();
				error = data.error || 'Failed to update password';
				success = '';
			}
		} catch (err) {
			console.error('Error updating password:', err);
			error = 'Failed to update password';
			success = '';
		}
	}
</script>

<div class="min-h-screen bg-slate-50 p-8">
	<div class="mx-auto max-w-2xl">
		<div class="mb-8 flex items-center justify-between">
			<h1 class="font-mono text-2xl">settings</h1>
			<button
				on:click={() => goto('/chat')}
				class="font-mono text-sm text-black hover:text-gray-600"
			>
				back to chat
			</button>
		</div>

		<div class="space-y-8">
			<!-- Profile Picture Section -->
			<div class="rounded-lg bg-white p-6 shadow-sm">
				<div class="flex items-center gap-6">
					<div class="h-24 w-24 overflow-hidden rounded-full bg-gray-200">
						{#if previewUrl}
							<img src={previewUrl} alt={currentUsername} class="h-full w-full object-cover" />
						{:else}
							<div class="flex h-full w-full items-center justify-center bg-black text-white">
								{currentUsername ? currentUsername[0].toUpperCase() : '?'}
							</div>
						{/if}
					</div>
					<input
						type="file"
						accept="image/*"
						on:change={handleFileSelect}
						class="font-mono text-sm"
					/>
				</div>
			</div>

			<!-- Username Section -->
			<div class="rounded-lg bg-white p-6 shadow-sm">
				<div class="space-y-4">
					<div>
						<label class="mb-2 block font-mono text-sm">Current Username</label>
						<input
							type="text"
							value={currentUsername}
							disabled
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm opacity-50"
						/>
					</div>
					<div>
						<label class="mb-2 block font-mono text-sm">New Username</label>
						<input
							bind:value={newUsername}
							type="text"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
				</div>
			</div>

			<!-- Password Section -->
			<div class="rounded-lg bg-white p-6 shadow-sm">
				<h2 class="mb-4 font-mono text-lg">change password</h2>
				<div class="space-y-4">
					<div>
						<label class="mb-2 block font-mono text-sm">Current Password</label>
						<input
							bind:value={currentPassword}
							type="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
					<div>
						<label class="mb-2 block font-mono text-sm">New Password</label>
						<input
							bind:value={newPassword}
							type="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
					<div>
						<label class="mb-2 block font-mono text-sm">Confirm New Password</label>
						<input
							bind:value={confirmPassword}
							type="password"
							class="w-full rounded-md border border-gray-300 p-2 font-mono text-sm focus:border-black focus:outline-none"
						/>
					</div>
				</div>
			</div>

			<!-- Action Buttons -->
			<div class="flex gap-4">
				<button
					on:click={updateProfile}
					class="flex-1 rounded-md bg-black px-4 py-2 font-mono text-sm text-white transition-colors hover:bg-gray-800"
				>
					save profile
				</button>
				<button
					on:click={updatePassword}
					class="flex-1 rounded-md border border-black bg-white px-4 py-2 font-mono text-sm transition-colors hover:bg-black hover:text-white"
				>
					update password
				</button>
			</div>

			{#if error}
				<p class="text-sm text-red-500">{error}</p>
			{/if}
			{#if success}
				<p class="text-sm text-green-500">{success}</p>
			{/if}
		</div>
	</div>
</div>

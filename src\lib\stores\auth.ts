import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface AuthState {
	token: string | null;
	username: string | null;
	isInitialized: boolean;
}

function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>({
		token: null,
		username: null,
		isInitialized: false
	});

	return {
		subscribe,
		setAuth: (token: string, username: string) => {
			set({ token, username, isInitialized: true });
		},
		clearAuth: () => {
			// Clear the cookie by setting it with an expired date
			if (browser) {
				document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
			}
			set({ token: null, username: null, isInitialized: true });
		},
		initializeAuth: async () => {
			if (!browser) return;

			try {
				// Try to get the token from cookies
				console.log('Auth: All cookies:', document.cookie);
				const tokenFromCookie = document.cookie
					.split('; ')
					.find(row => row.startsWith('token='))
					?.split('=')[1];

				if (tokenFromCookie) {
					console.log('Auth: Found token in cookie (first 20 chars):', tokenFromCookie.substring(0, 20) + '...');
					// Validate the token
					const res = await fetch('/api/auth/validate', { credentials: 'include' });
					console.log('Auth: Validation response status:', res.status);
					if (res.ok) {
						const data = await res.json();
						console.log('Auth: Token validation successful, username:', data.username);
						set({ token: tokenFromCookie, username: data.username, isInitialized: true });
					} else {
						const errorData = await res.text();
						console.log('Auth: Token validation failed:', errorData);
						set({ token: null, username: null, isInitialized: true });
					}
				} else {
					console.log('Auth: No token found in cookie');
					set({ token: null, username: null, isInitialized: true });
				}
			} catch (err) {
				console.error('Error initializing auth:', err);
				set({ token: null, username: null, isInitialized: true });
			}
		},
		markInitialized: () => {
			update(state => ({ ...state, isInitialized: true }));
		}
	};
}

export const auth = createAuthStore();

import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface AuthState {
	token: string | null;
	username: string | null;
	isInitialized: boolean;
}

function createAuthStore() {
	const { subscribe, set, update } = writable<AuthState>({
		token: null,
		username: null,
		isInitialized: false
	});

	return {
		subscribe,
		setAuth: (token: string, username: string) => {
			if (browser) {
				localStorage.setItem('auth_token', token);
				localStorage.setItem('auth_username', username);
			}
			set({ token, username, isInitialized: true });
		},
		clearAuth: () => {
			if (browser) {
				localStorage.removeItem('auth_token');
				localStorage.removeItem('auth_username');
				// Also clear cookie as fallback
				document.cookie = 'token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
			}
			set({ token: null, username: null, isInitialized: true });
		},
		initializeAuth: async () => {
			if (!browser) return;

			try {
				// Try to get the token from localStorage first, then fallback to cookies
				const tokenFromStorage = localStorage.getItem('auth_token');
				const usernameFromStorage = localStorage.getItem('auth_username');

				console.log('Auth: Checking localStorage - token:', !!tokenFromStorage, 'username:', usernameFromStorage);

				if (tokenFromStorage && usernameFromStorage) {
					console.log('Auth: Found auth in localStorage');
					set({ token: tokenFromStorage, username: usernameFromStorage, isInitialized: true });
					return;
				}

				// Fallback: check cookies
				console.log('Auth: Checking cookies as fallback');
				const tokenFromCookie = document.cookie
					.split('; ')
					.find(row => row.startsWith('token='))
					?.split('=')[1];

				if (tokenFromCookie) {
					console.log('Auth: Found token in cookie, validating...');
					const res = await fetch('/api/auth/validate', { credentials: 'include' });
					if (res.ok) {
						const data = await res.json();
						console.log('Auth: Cookie validation successful, migrating to localStorage');
						// Migrate to localStorage
						localStorage.setItem('auth_token', tokenFromCookie);
						localStorage.setItem('auth_username', data.username);
						set({ token: tokenFromCookie, username: data.username, isInitialized: true });
					} else {
						console.log('Auth: Cookie validation failed');
						set({ token: null, username: null, isInitialized: true });
					}
				} else {
					console.log('Auth: No authentication found');
					set({ token: null, username: null, isInitialized: true });
				}
			} catch (err) {
				console.error('Error initializing auth:', err);
				set({ token: null, username: null, isInitialized: true });
			}
		},
		markInitialized: () => {
			update(state => ({ ...state, isInitialized: true }));
		}
	};
}

export const auth = createAuthStore();

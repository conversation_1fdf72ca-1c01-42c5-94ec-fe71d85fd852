import { json } from '@sveltejs/kit';
import jwt from 'jsonwebtoken';
import type { RequestHandler } from '@sveltejs/kit';
import { connect } from '$lib/db';
import User from '$lib/models/User';
import { ENV } from '$lib/config/env';
import { getTokenFromRequest } from '$lib/utils/auth';

export const GET: RequestHandler = async (event) => {
	const token = getTokenFromRequest(event);

	if (!token) {
		return json({ error: 'No token provided' }, { status: 401 });
	}

	try {
		// Verify JWT token first (no DB connection needed)
		const decoded = jwt.verify(token, ENV.JWT_SECRET) as unknown;

		if (typeof decoded === 'object' && decoded !== null && 'userId' in decoded) {
			const userId = (decoded as { userId: string }).userId;

			// Connect to database only when needed
			await connect();
			const user = await User.findById(userId).lean(); // Use lean() for better performance

			if (!user) {
				return json({ error: 'Invalid token' }, { status: 401 });
			}

			return json({ username: user.username });
		}

		return json({ error: 'Invalid token' }, { status: 401 });
	} catch (err) {
		console.error('Error verifying token:', err);
		return json({ error: 'Invalid token' }, { status: 401 });
	}
};

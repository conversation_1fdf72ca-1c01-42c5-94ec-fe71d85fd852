import type { RequestEvent } from '@sveltejs/kit';

/**
 * Extract token from either Authorization header or cookies
 */
export function getTokenFromRequest(event: RequestEvent): string | null {
    // First try Authorization header
    const authHeader = event.request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }

    // Fallback to cookies
    return event.cookies.get('token') || null;
}

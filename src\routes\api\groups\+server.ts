import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Group from '$lib/models/Group';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import { ENV } from '$lib/config/env';

export const GET: RequestHandler = async ({ cookies }) => {
	const token = cookies.get('token');
	if (!token) return json({ error: 'Unauthorized' }, { status: 401 });

	try {
		await connect();
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);

		const groups = await Group.find({
			members: decoded.userId
		})
			.populate('members', 'username') // Keep this
			.populate('createdBy', 'username');

		return json({ groups });
	} catch (err) {
		console.error('Error fetching groups:', err);
		return json({ error: 'Failed to fetch groups' }, { status: 500 });
	}
};

export const POST: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) return json({ error: 'Unauthorized' }, { status: 401 });

	try {
		await connect();
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const { name, members } = await request.json();

		// Convert usernames to user IDs
		const memberUsers = await User.find({ username: { $in: members } });
		const memberIds = memberUsers.map((user) => user._id);

		// Create the group with member IDs
		const group = await Group.create({
			name,
			members: [...memberIds, decoded.userId], // Include the creator
			createdBy: decoded.userId
		});

		// Populate the members for the response
		const populatedGroup = await Group.findById(group._id).populate('members', 'username');

		return json({ group: populatedGroup });
	} catch (err) {
		console.error('Error creating group:', err);
		return json({ error: 'Failed to create group' }, { status: 500 });
	}
};

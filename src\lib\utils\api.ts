import { browser } from '$app/environment';

/**
 * Make an authenticated fetch request with token from localStorage
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
    const headers = new Headers(options.headers);
    
    // Add Authorization header if we have a token
    if (browser) {
        const token = localStorage.getItem('auth_token');
        if (token) {
            headers.set('Authorization', `Bearer ${token}`);
        }
    }
    
    // Always include credentials for cookie fallback
    return fetch(url, {
        ...options,
        headers,
        credentials: 'include'
    });
}

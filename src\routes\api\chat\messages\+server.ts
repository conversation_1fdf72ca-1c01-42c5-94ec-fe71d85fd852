import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@sveltejs/kit';
import { connect } from '$lib/db';
import Chat from '$lib/models/Chat';
import User from '$lib/models/User';
import jwt from 'jsonwebtoken';
import { decrypt } from '$lib/utils/encryption';
import { ENV } from '$lib/config/env';

export const GET: RequestHandler = async ({ request, cookies }) => {
	const token = cookies.get('token');
	if (!token) {
		return new Response(JSON.stringify({ error: 'Unauthorized' }), { status: 401 });
	}

	try {
		const decoded: any = jwt.verify(token, ENV.JWT_SECRET);
		const receiver = new URL(request.url).searchParams.get('receiver');
		const groupId = new URL(request.url).searchParams.get('group');

		await connect();

		let messages;

		if (groupId) {
			messages = await Chat.find({
				group: groupId
			})
				.populate('sender', 'username')
				.sort({ timestamp: 1 });
		} else if (receiver) {
			const receiverUser = await User.findOne({ username: receiver });
			if (!receiverUser) {
				return new Response(JSON.stringify({ error: 'Receiver not found' }), { status: 404 });
			}

			messages = await Chat.find({
				$or: [
					{ sender: decoded.userId, receiver: receiverUser._id },
					{ sender: receiverUser._id, receiver: decoded.userId }
				]
			})
				.populate('sender', 'username')
				.sort({ timestamp: 1 });
		} else {
			return new Response(JSON.stringify({ error: 'Receiver or group ID required' }), {
				status: 400
			});
		}

		// Messages are automatically decrypted by the mongoose schema
		const formattedMessages = messages.map((msg: any) => ({
			_id: msg._id,
			sender: msg.sender.username,
			message: msg.message, // Already decrypted
			timestamp: msg.timestamp,
			attachments: msg.attachments || []
		}));

		return new Response(JSON.stringify(formattedMessages), { status: 200 });
	} catch (err) {
		console.error('Error fetching messages:', err);
		return new Response(JSON.stringify({ error: 'Failed to fetch messages' }), { status: 500 });
	}
};
